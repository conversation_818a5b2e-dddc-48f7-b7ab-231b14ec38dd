"""
FastAPI REST API for the AI Companion System.
Provides HTTP endpoints for conversation, analytics, and system management.
"""

import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any

from fastapi import FastAPI, HTTPException, Depends, BackgroundTasks, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from pydantic import BaseModel, Field

from ..core.conversation import ConversationService
from ..core.models import InteractionType, EmotionType, MentalHealthRisk
from ..mental_health.analytics import MentalHealthAnalytics
from ..mental_health.crisis_detection import CrisisDetectionService
from ..config.settings import settings
from ..utils.helpers import generate_id, utc_now


# Request/Response Models
class MessageRequest(BaseModel):
    """Request model for sending a message."""
    user_id: str = Field(..., description="Unique user identifier")
    message: str = Field(..., min_length=1, max_length=5000, description="User message")
    interaction_type: InteractionType = Field(InteractionType.CONVERSATION, description="Type of interaction")
    context: Optional[Dict[str, Any]] = Field(None, description="Additional context")


class MessageResponse(BaseModel):
    """Response model for message processing."""
    response: str = Field(..., description="AI companion response")
    emotional_state: Dict[str, Any] = Field(..., description="Analyzed emotional state")
    risk_level: MentalHealthRisk = Field(..., description="Mental health risk assessment")
    memory_insights: Optional[Dict[str, Any]] = Field(None, description="Memory-related insights")
    suggested_actions: Optional[List[Dict[str, Any]]] = Field(None, description="Suggested follow-up actions")
    conversation_id: str = Field(..., description="Conversation identifier")
    timestamp: datetime = Field(..., description="Response timestamp")


class UserProfileRequest(BaseModel):
    """Request model for user profile operations."""
    name: Optional[str] = None
    preferences: Optional[Dict[str, Any]] = None
    personality_traits: Optional[Dict[str, Any]] = None
    communication_style: Optional[Dict[str, Any]] = None


class AnalyticsRequest(BaseModel):
    """Request model for analytics queries."""
    user_id: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    emotion_filter: Optional[List[EmotionType]] = None
    risk_level_filter: Optional[List[MentalHealthRisk]] = None


class CrisisResponse(BaseModel):
    """Response model for crisis detection."""
    is_crisis: bool = Field(..., description="Whether crisis was detected")
    risk_level: MentalHealthRisk = Field(..., description="Risk level assessment")
    crisis_type: Optional[str] = Field(None, description="Type of crisis detected")
    intervention_message: str = Field(..., description="Crisis intervention message")
    resources: List[Dict[str, str]] = Field(..., description="Crisis support resources")


# Security
security = HTTPBearer(auto_error=False)


async def verify_api_key(credentials: HTTPAuthorizationCredentials = Depends(security)):
    """Verify API key for protected endpoints."""
    if not credentials:
        if settings.api_key_required:
            raise HTTPException(status_code=401, detail="API key required")
        return None
    
    if credentials.credentials != settings.api_key:
        raise HTTPException(status_code=401, detail="Invalid API key")
    
    return credentials.credentials


def create_api_app(
    conversation_service: ConversationService,
    mental_health_analytics: MentalHealthAnalytics,
    crisis_detection: CrisisDetectionService = None
) -> FastAPI:
    """Create and configure the FastAPI application."""
    
    app = FastAPI(
        title="AI Companion API",
        description="REST API for the AI Companion System - Emotional Support & Mental Health Assistant",
        version="1.0.0",
        docs_url="/docs" if settings.debug_mode else None,
        redoc_url="/redoc" if settings.debug_mode else None
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=settings.allowed_origins,
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Logging
    logger = logging.getLogger(__name__)
    
    # Request tracking
    request_count = {"total": 0, "errors": 0}
    
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """Log all requests for monitoring."""
        start_time = datetime.now(timezone.utc)
        request_count["total"] += 1
        
        try:
            response = await call_next(request)
            
            # Log request details
            duration = (datetime.now(timezone.utc) - start_time).total_seconds()
            logger.info(
                f"API Request: {request.method} {request.url.path} - "
                f"Status: {response.status_code} - Duration: {duration:.3f}s"
            )
            
            return response
            
        except Exception as e:
            request_count["errors"] += 1
            logger.error(f"API Error: {request.method} {request.url.path} - Error: {str(e)}")
            raise
    
    # Health check endpoint
    @app.get("/health", tags=["System"])
    async def health_check():
        """Health check endpoint."""
        return {
            "status": "healthy",
            "timestamp": utc_now(),
            "version": "1.0.0",
            "requests": request_count
        }
    
    # Conversation endpoints
    @app.post("/conversation/message", response_model=MessageResponse, tags=["Conversation"])
    async def send_message(
        request: MessageRequest,
        background_tasks: BackgroundTasks,
        api_key: str = Depends(verify_api_key)
    ):
        """Send a message to the AI companion."""
        try:
            # Process message
            response = await conversation_service.process_message(
                user_id=request.user_id,
                message=request.message,
                interaction_type=request.interaction_type,
                context=request.context or {}
            )
            
            if not response:
                raise HTTPException(status_code=500, detail="Failed to process message")
            
            # Check for crisis situation
            if crisis_detection and response.get("risk_level") in ["high", "critical"]:
                background_tasks.add_task(
                    _handle_crisis_background,
                    request.user_id,
                    response
                )
            
            return MessageResponse(
                response=response.get("response", ""),
                emotional_state=response.get("emotional_state", {}),
                risk_level=response.get("risk_level", MentalHealthRisk.LOW),
                memory_insights=response.get("memory_insights"),
                suggested_actions=response.get("suggested_actions"),
                conversation_id=response.get("conversation_id", generate_id()),
                timestamp=utc_now()
            )
            
        except Exception as e:
            logger.error(f"Error processing message: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    @app.get("/conversation/history/{user_id}", tags=["Conversation"])
    async def get_conversation_history(
        user_id: str,
        limit: int = 50,
        api_key: str = Depends(verify_api_key)
    ):
        """Get conversation history for a user."""
        try:
            # Get conversation history from memory service
            memories = await conversation_service.memory_service.get_personal_memories(
                user_id=user_id,
                limit=limit
            )
            
            history = []
            for memory in memories:
                if memory.interaction_type == InteractionType.CONVERSATION:
                    history.append({
                        "memory_id": memory.memory_id,
                        "content": memory.content,
                        "emotional_context": memory.emotional_context,
                        "importance_score": memory.importance_score,
                        "timestamp": memory.timestamp
                    })
            
            return {
                "user_id": user_id,
                "history": history,
                "total_count": len(history)
            }
            
        except Exception as e:
            logger.error(f"Error getting conversation history: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    # User profile endpoints
    @app.get("/user/profile/{user_id}", tags=["User Management"])
    async def get_user_profile(
        user_id: str,
        api_key: str = Depends(verify_api_key)
    ):
        """Get user profile."""
        try:
            profile = await conversation_service.memory_service.storage_service.get_user_profile(user_id)
            
            if not profile:
                raise HTTPException(status_code=404, detail="User profile not found")
            
            return profile.model_dump()
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting user profile: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    @app.put("/user/profile/{user_id}", tags=["User Management"])
    async def update_user_profile(
        user_id: str,
        profile_update: UserProfileRequest,
        api_key: str = Depends(verify_api_key)
    ):
        """Update user profile."""
        try:
            # Get existing profile or create new one
            existing_profile = await conversation_service.memory_service.storage_service.get_user_profile(user_id)
            
            if existing_profile:
                # Update existing profile
                if profile_update.name is not None:
                    existing_profile.name = profile_update.name
                if profile_update.preferences is not None:
                    existing_profile.preferences.update(profile_update.preferences)
                if profile_update.personality_traits is not None:
                    existing_profile.personality_traits.update(profile_update.personality_traits)
                if profile_update.communication_style is not None:
                    existing_profile.communication_style.update(profile_update.communication_style)
                
                profile = existing_profile
            else:
                # Create new profile
                from ..core.models import UserProfile
                profile = UserProfile(
                    user_id=user_id,
                    name=profile_update.name or "Anonymous",
                    preferences=profile_update.preferences or {},
                    personality_traits=profile_update.personality_traits or {},
                    communication_style=profile_update.communication_style or {}
                )
            
            # Save profile
            success = await conversation_service.memory_service.storage_service.store_user_profile(profile)
            
            if not success:
                raise HTTPException(status_code=500, detail="Failed to update profile")
            
            return profile.model_dump()
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error updating user profile: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    # Analytics endpoints
    @app.post("/analytics/query", tags=["Analytics"])
    async def query_analytics(
        request: AnalyticsRequest,
        api_key: str = Depends(verify_api_key)
    ):
        """Query mental health analytics."""
        try:
            analytics = await mental_health_analytics.generate_insights(
                user_id=request.user_id,
                start_date=request.start_date,
                end_date=request.end_date,
                filters={
                    "emotions": request.emotion_filter,
                    "risk_levels": request.risk_level_filter
                }
            )
            
            return analytics
            
        except Exception as e:
            logger.error(f"Error querying analytics: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    @app.get("/analytics/summary", tags=["Analytics"])
    async def get_analytics_summary(
        days: int = 30,
        api_key: str = Depends(verify_api_key)
    ):
        """Get analytics summary for the specified period."""
        try:
            end_date = utc_now()
            start_date = end_date - timedelta(days=days)
            
            summary = await mental_health_analytics.get_platform_summary(
                start_date=start_date,
                end_date=end_date
            )
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting analytics summary: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    # Crisis detection endpoints
    @app.post("/crisis/analyze", response_model=CrisisResponse, tags=["Crisis Detection"])
    async def analyze_crisis(
        user_id: str,
        message: str,
        api_key: str = Depends(verify_api_key)
    ):
        """Analyze message for crisis indicators."""
        try:
            if not crisis_detection:
                raise HTTPException(status_code=501, detail="Crisis detection not available")
            
            result = await crisis_detection.analyze_message(message, user_id)
            
            return CrisisResponse(
                is_crisis=result.get("is_crisis", False),
                risk_level=result.get("risk_level", MentalHealthRisk.LOW),
                crisis_type=result.get("crisis_type"),
                intervention_message=result.get("intervention_message", ""),
                resources=result.get("resources", [])
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error analyzing crisis: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    # System management endpoints
    @app.get("/system/stats", tags=["System"])
    async def get_system_stats(api_key: str = Depends(verify_api_key)):
        """Get system statistics."""
        try:
            # Get storage stats
            storage_stats = await conversation_service.memory_service.storage_service.get_storage_stats()
            
            # Get conversation service stats
            conversation_stats = {
                "active_conversations": len(conversation_service.active_conversations),
                "total_requests": request_count["total"],
                "error_count": request_count["errors"]
            }
            
            return {
                "storage": storage_stats,
                "conversations": conversation_stats,
                "timestamp": utc_now()
            }
            
        except Exception as e:
            logger.error(f"Error getting system stats: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    @app.post("/system/cleanup", tags=["System"])
    async def cleanup_old_data(
        days_old: int = 90,
        api_key: str = Depends(verify_api_key)
    ):
        """Clean up old data."""
        try:
            await conversation_service.memory_service.storage_service.cleanup_old_data(days_old)
            
            return {
                "status": "success",
                "message": f"Cleaned up data older than {days_old} days",
                "timestamp": utc_now()
            }
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            raise HTTPException(status_code=500, detail="Internal server error")
    
    # Error handlers
    @app.exception_handler(HTTPException)
    async def http_exception_handler(request: Request, exc: HTTPException):
        """Handle HTTP exceptions."""
        return JSONResponse(
            status_code=exc.status_code,
            content={
                "error": exc.detail,
                "status_code": exc.status_code,
                "timestamp": utc_now().isoformat()
            }
        )
    
    @app.exception_handler(Exception)
    async def general_exception_handler(request: Request, exc: Exception):
        """Handle general exceptions."""
        logger.error(f"Unhandled exception: {exc}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Internal server error",
                "status_code": 500,
                "timestamp": utc_now().isoformat()
            }
        )
    
    async def _handle_crisis_background(user_id: str, response: Dict[str, Any]):
        """Handle crisis situation in background."""
        try:
            if crisis_detection:
                await crisis_detection.handle_crisis_intervention(user_id, response)
        except Exception as e:
            logger.error(f"Error handling crisis in background: {e}")
    
    return app


# Standalone ASGI application
def create_standalone_app():
    """Create standalone API app for deployment."""
    from ..main import AICompanionSystem
    
    # This would be initialized by the main system
    # For standalone deployment, you'd need to initialize services here
    app = FastAPI(
        title="AI Companion API",
        description="Standalone AI Companion API",
        version="1.0.0"
    )
    
    @app.get("/")
    async def root():
        return {"message": "AI Companion API - Please initialize system first"}
    
    return app


if __name__ == "__main__":
    import uvicorn
    
    app = create_standalone_app()
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=settings.api_port,
        log_level="info"
    )
