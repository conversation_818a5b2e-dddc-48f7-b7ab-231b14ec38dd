"""
Emotional Intelligence Service for the AI Companion System.
Advanced emotional analysis, therapeutic techniques, and empathetic response generation.
"""

import asyncio
import logging
import re
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter

import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

from .models import (
    EmotionType, InteractionType, TherapeuticTechnique, MentalHealthRisk,
    EmotionalState, calculate_memory_importance, utc_now
)
from ..services.gemini import GeminiService
from ..utils.helpers import extract_keywords, calculate_similarity
from ..config.settings import settings


class EmotionalIntelligenceService:
    """Advanced emotional intelligence with therapeutic capabilities."""
    
    def __init__(self, gemini_service: GeminiService):
        """Initialize emotional intelligence service."""
        self.logger = logging.getLogger(__name__)
        self.gemini_service = gemini_service
        
        # Emotional pattern recognition
        self.emotion_patterns = self._initialize_emotion_patterns()
        self.crisis_indicators = self._initialize_crisis_indicators()
        self.therapeutic_responses = self._initialize_therapeutic_responses()
        
        # User emotional history tracking
        self.user_emotional_history: Dict[str, List[EmotionalState]] = defaultdict(list)
        self.user_patterns: Dict[str, Dict[str, Any]] = defaultdict(dict)
        
        # TF-IDF vectorizer for text analysis
        self.vectorizer = TfidfVectorizer(
            max_features=1000,
            stop_words='english',
            ngram_range=(1, 2)
        )
        
        self.logger.info("✅ Emotional Intelligence Service initialized")
    
    def _initialize_emotion_patterns(self) -> Dict[EmotionType, List[str]]:
        """Initialize emotion detection patterns."""
        return {
            EmotionType.JOY: [
                r'\b(happy|joy|excited|thrilled|elated|cheerful|delighted|pleased|glad|wonderful)\b',
                r'\b(amazing|fantastic|great|awesome|brilliant|excellent|perfect|love it)\b',
                r'\b(celebrating|celebration|achievement|success|accomplished|proud)\b'
            ],
            EmotionType.SADNESS: [
                r'\b(sad|depressed|down|blue|melancholy|gloomy|miserable|heartbroken)\b',
                r'\b(crying|tears|weeping|sobbing|grief|mourning|loss|lonely)\b',
                r'\b(disappointed|discouraged|hopeless|despair|devastated)\b'
            ],
            EmotionType.ANGER: [
                r'\b(angry|mad|furious|rage|irritated|annoyed|frustrated|pissed)\b',
                r'\b(hate|disgusted|outraged|livid|seething|fuming|bitter)\b',
                r'\b(betrayed|wronged|unfair|injustice|violated)\b'
            ],
            EmotionType.FEAR: [
                r'\b(afraid|scared|terrified|frightened|anxious|worried|nervous|panic)\b',
                r'\b(fear|phobia|dread|apprehensive|uneasy|concerned|alarmed)\b',
                r'\b(overwhelmed|stressed|tension|pressure|uncertainty)\b'
            ],
            EmotionType.ANXIETY: [
                r'\b(anxious|anxiety|worried|stress|nervous|tense|restless)\b',
                r'\b(panic|overwhelmed|racing thoughts|can\'t stop thinking)\b',
                r'\b(what if|worst case|catastrophizing|spiraling)\b'
            ],
            EmotionType.LONELINESS: [
                r'\b(lonely|alone|isolated|disconnected|abandoned|empty)\b',
                r'\b(no one understands|nobody cares|by myself|solitude)\b',
                r'\b(missing|longing|yearning|distant|withdrawn)\b'
            ],
            EmotionType.LOVE: [
                r'\b(love|adore|cherish|treasure|devoted|affection|care deeply)\b',
                r'\b(soulmate|beloved|darling|precious|meaningful relationship)\b',
                r'\b(connected|bonded|intimate|close|special someone)\b'
            ],
            EmotionType.GUILT: [
                r'\b(guilty|guilt|ashamed|regret|sorry|fault|blame myself)\b',
                r'\b(should have|shouldn\'t have|if only|mistake|wrong)\b',
                r'\b(disappointed in myself|let down|failed)\b'
            ],
            EmotionType.SHAME: [
                r'\b(shame|ashamed|embarrassed|humiliated|mortified|disgrace)\b',
                r'\b(worthless|inadequate|not good enough|failure|pathetic)\b',
                r'\b(hide|can\'t face|avoid|withdraw|disappear)\b'
            ]
        }
    
    def _initialize_crisis_indicators(self) -> Dict[str, List[str]]:
        """Initialize crisis detection patterns."""
        return {
            "suicide_ideation": [
                r'\b(kill myself|end it all|suicide|take my life|don\'t want to live)\b',
                r'\b(better off dead|no point living|want to die|end the pain)\b',
                r'\b(suicide plan|how to die|ways to kill|ending it)\b'
            ],
            "self_harm": [
                r'\b(cut myself|hurt myself|self harm|cutting|burning myself)\b',
                r'\b(deserve pain|punish myself|make it stop|physical pain)\b',
                r'\b(razor|blade|pills|overdose|harm)\b'
            ],
            "severe_depression": [
                r'\b(can\'t go on|no hope|hopeless|pointless|empty inside)\b',
                r'\b(nothing matters|can\'t feel|numb|void|darkness)\b',
                r'\b(burden|worthless|everyone better without me)\b'
            ],
            "panic_crisis": [
                r'\b(can\'t breathe|heart racing|dying|losing control|going crazy)\b',
                r'\b(panic attack|hyperventilating|chest pain|dizzy|faint)\b',
                r'\b(emergency|help me|can\'t stop|overwhelming fear)\b'
            ]
        }
    
    def _initialize_therapeutic_responses(self) -> Dict[TherapeuticTechnique, List[str]]:
        """Initialize therapeutic response templates."""
        return {
            TherapeuticTechnique.VALIDATION: [
                "Your feelings are completely valid and understandable.",
                "It makes perfect sense that you would feel this way given what you're going through.",
                "Thank you for trusting me with these difficult emotions.",
                "Anyone in your situation would likely feel similar emotions."
            ],
            TherapeuticTechnique.ACTIVE_LISTENING: [
                "I hear that you're feeling {emotion}. Can you tell me more about that?",
                "It sounds like {situation} is really affecting you. What's that like for you?",
                "Help me understand what this experience has been like for you.",
                "I'm listening. What else is on your mind about this?"
            ],
            TherapeuticTechnique.COGNITIVE_REFRAMING: [
                "I wonder if there might be another way to look at this situation?",
                "What would you tell a good friend who was going through the same thing?",
                "Are there any other possible explanations for what happened?",
                "What evidence do we have for and against this thought?"
            ],
            TherapeuticTechnique.MINDFULNESS: [
                "Let's take a moment to focus on the present. What do you notice around you right now?",
                "Can you feel your feet on the ground? Let's breathe together for a moment.",
                "Notice what you're experiencing right now without trying to change it.",
                "What are three things you can see, hear, or feel in this moment?"
            ],
            TherapeuticTechnique.GROUNDING: [
                "Let's try the 5-4-3-2-1 technique: 5 things you can see, 4 you can touch, 3 you can hear, 2 you can smell, 1 you can taste.",
                "Feel your feet on the ground. You are safe in this moment.",
                "Take a deep breath with me. In for 4, hold for 4, out for 6.",
                "Focus on something in your environment that feels solid and real."
            ]
        }
    
    async def analyze_emotion(
        self,
        text: str,
        user_id: str = None,
        context: Dict[str, Any] = None
    ) -> EmotionalState:
        """Analyze emotional content with advanced AI and pattern recognition."""
        try:
            # Pattern-based emotion detection
            pattern_emotions = self._detect_emotions_by_pattern(text)
            
            # AI-powered emotional analysis
            ai_analysis = await self.gemini_service.analyze_emotional_content(text)
            
            # Combine analyses
            primary_emotion, intensity = self._combine_emotion_analyses(
                pattern_emotions, ai_analysis, text
            )
            
            # Detect secondary emotions
            secondary_emotions = self._detect_secondary_emotions(text, primary_emotion)
            
            # Assess mental health risk
            risk_level = self._assess_mental_health_risk(text, primary_emotion, intensity)
            
            # Identify context factors
            context_factors = self._identify_context_factors(text, context)
            
            # Create emotional state
            emotional_state = EmotionalState(
                primary_emotion=primary_emotion,
                intensity=intensity,
                secondary_emotions=secondary_emotions,
                context_factors=context_factors,
                risk_level=risk_level,
                timestamp=utc_now()
            )
            
            # Track user emotional history
            if user_id:
                self._update_user_emotional_history(user_id, emotional_state)
            
            return emotional_state
            
        except Exception as e:
            self.logger.error(f"Error analyzing emotion: {e}")
            return EmotionalState(
                primary_emotion=EmotionType.NEUTRAL,
                intensity=0.5,
                risk_level=MentalHealthRisk.LOW
            )
    
    def _detect_emotions_by_pattern(self, text: str) -> Dict[EmotionType, float]:
        """Detect emotions using regex patterns."""
        text_lower = text.lower()
        emotion_scores = {}
        
        for emotion, patterns in self.emotion_patterns.items():
            score = 0.0
            for pattern in patterns:
                matches = len(re.findall(pattern, text_lower, re.IGNORECASE))
                score += matches * 0.3  # Weight per match
            
            if score > 0:
                emotion_scores[emotion] = min(score, 1.0)  # Cap at 1.0
        
        return emotion_scores
    
    def _combine_emotion_analyses(
        self,
        pattern_emotions: Dict[EmotionType, float],
        ai_analysis: Dict[str, Any],
        text: str
    ) -> Tuple[EmotionType, float]:
        """Combine pattern and AI emotion analyses."""
        # Start with pattern-based emotions
        combined_scores = pattern_emotions.copy()
        
        # Add AI analysis if available
        if ai_analysis and "analysis" in ai_analysis:
            # Simple keyword extraction from AI analysis
            # In production, you'd want more sophisticated parsing
            ai_text = ai_analysis["analysis"].lower()
            
            for emotion in EmotionType:
                if emotion.value in ai_text:
                    current_score = combined_scores.get(emotion, 0.0)
                    combined_scores[emotion] = min(current_score + 0.4, 1.0)
        
        # Determine primary emotion and intensity
        if combined_scores:
            primary_emotion = max(combined_scores, key=combined_scores.get)
            intensity = combined_scores[primary_emotion]
        else:
            # Fallback to neutral
            primary_emotion = EmotionType.NEUTRAL
            intensity = 0.5
        
        return primary_emotion, intensity
    
    def _detect_secondary_emotions(
        self,
        text: str,
        primary_emotion: EmotionType
    ) -> Dict[EmotionType, float]:
        """Detect secondary emotions present in the text."""
        pattern_emotions = self._detect_emotions_by_pattern(text)
        
        # Remove primary emotion and filter by threshold
        secondary_emotions = {
            emotion: score for emotion, score in pattern_emotions.items()
            if emotion != primary_emotion and score >= 0.2
        }
        
        return secondary_emotions
    
    def _assess_mental_health_risk(
        self,
        text: str,
        primary_emotion: EmotionType,
        intensity: float
    ) -> MentalHealthRisk:
        """Assess mental health risk level."""
        text_lower = text.lower()
        risk_score = 0.0
        
        # Check for crisis indicators
        for crisis_type, patterns in self.crisis_indicators.items():
            for pattern in patterns:
                if re.search(pattern, text_lower, re.IGNORECASE):
                    if crisis_type in ["suicide_ideation", "self_harm"]:
                        risk_score += 0.8
                    elif crisis_type == "severe_depression":
                        risk_score += 0.6
                    elif crisis_type == "panic_crisis":
                        risk_score += 0.5
        
        # Factor in emotion intensity
        if primary_emotion in [EmotionType.SADNESS, EmotionType.ANXIETY, EmotionType.FEAR]:
            risk_score += intensity * 0.3
        
        # Determine risk level
        if risk_score >= 0.7:
            return MentalHealthRisk.CRITICAL
        elif risk_score >= 0.4:
            return MentalHealthRisk.HIGH
        elif risk_score >= 0.2:
            return MentalHealthRisk.MODERATE
        else:
            return MentalHealthRisk.LOW
    
    def _identify_context_factors(
        self,
        text: str,
        context: Dict[str, Any] = None
    ) -> List[str]:
        """Identify contextual factors affecting emotion."""
        factors = []
        text_lower = text.lower()
        
        # Relationship factors
        if any(word in text_lower for word in ["relationship", "partner", "boyfriend", "girlfriend", "spouse", "marriage"]):
            factors.append("relationship_issues")
        
        # Work/career factors
        if any(word in text_lower for word in ["work", "job", "career", "boss", "colleague", "workplace"]):
            factors.append("work_stress")
        
        # Family factors
        if any(word in text_lower for word in ["family", "parents", "mother", "father", "siblings", "children"]):
            factors.append("family_dynamics")
        
        # Health factors
        if any(word in text_lower for word in ["health", "sick", "illness", "doctor", "medical", "pain"]):
            factors.append("health_concerns")
        
        # Financial factors
        if any(word in text_lower for word in ["money", "financial", "debt", "bills", "income", "budget"]):
            factors.append("financial_stress")
        
        # Social factors
        if any(word in text_lower for word in ["friends", "social", "party", "gathering", "community"]):
            factors.append("social_situation")
        
        # Add context from external sources
        if context:
            time_of_day = context.get("time_of_day")
            if time_of_day:
                factors.append(f"time_{time_of_day}")
        
        return factors
    
    def _update_user_emotional_history(self, user_id: str, emotional_state: EmotionalState):
        """Update user's emotional history and patterns."""
        # Add to history
        self.user_emotional_history[user_id].append(emotional_state)
        
        # Keep only recent history (last 100 entries)
        if len(self.user_emotional_history[user_id]) > 100:
            self.user_emotional_history[user_id] = self.user_emotional_history[user_id][-100:]
        
        # Update patterns
        self._update_user_patterns(user_id)
    
    def _update_user_patterns(self, user_id: str):
        """Update user emotional patterns."""
        history = self.user_emotional_history[user_id]
        if len(history) < 5:  # Need minimum history
            return
        
        # Analyze patterns
        recent_emotions = [state.primary_emotion for state in history[-20:]]
        emotion_frequency = Counter(recent_emotions)
        
        # Calculate average intensity by emotion
        emotion_intensities = defaultdict(list)
        for state in history[-20:]:
            emotion_intensities[state.primary_emotion].append(state.intensity)
        
        avg_intensities = {
            emotion: np.mean(intensities)
            for emotion, intensities in emotion_intensities.items()
        }
        
        # Identify trends
        recent_risk_levels = [state.risk_level for state in history[-10:]]
        risk_trend = self._calculate_risk_trend(recent_risk_levels)
        
        # Update patterns
        self.user_patterns[user_id] = {
            "dominant_emotions": emotion_frequency.most_common(3),
            "average_intensities": avg_intensities,
            "risk_trend": risk_trend,
            "last_updated": utc_now()
        }
    
    def _calculate_risk_trend(self, risk_levels: List[MentalHealthRisk]) -> str:
        """Calculate trend in mental health risk."""
        if len(risk_levels) < 3:
            return "insufficient_data"
        
        # Convert to numeric values
        risk_values = {
            MentalHealthRisk.LOW: 1,
            MentalHealthRisk.MODERATE: 2,
            MentalHealthRisk.HIGH: 3,
            MentalHealthRisk.CRITICAL: 4
        }
        
        numeric_risks = [risk_values[risk] for risk in risk_levels]
        
        # Simple trend calculation
        if len(numeric_risks) >= 3:
            recent_avg = np.mean(numeric_risks[-3:])
            earlier_avg = np.mean(numeric_risks[:-3]) if len(numeric_risks) > 3 else recent_avg
            
            if recent_avg > earlier_avg + 0.5:
                return "increasing"
            elif recent_avg < earlier_avg - 0.5:
                return "decreasing"
            else:
                return "stable"
        
        return "stable"
    
    async def suggest_therapeutic_technique(
        self,
        emotional_state: EmotionalState,
        user_id: str = None
    ) -> TherapeuticTechnique:
        """Suggest appropriate therapeutic technique."""
        primary_emotion = emotional_state.primary_emotion
        intensity = emotional_state.intensity
        risk_level = emotional_state.risk_level
        
        # Crisis intervention for high risk
        if risk_level in [MentalHealthRisk.CRITICAL, MentalHealthRisk.HIGH]:
            return TherapeuticTechnique.CRISIS_INTERVENTION
        
        # Grounding for panic/anxiety
        if primary_emotion in [EmotionType.ANXIETY, EmotionType.FEAR] and intensity > 0.7:
            return TherapeuticTechnique.GROUNDING
        
        # Validation for intense negative emotions
        if primary_emotion in [EmotionType.SADNESS, EmotionType.ANGER, EmotionType.SHAME] and intensity > 0.6:
            return TherapeuticTechnique.VALIDATION
        
        # Cognitive reframing for moderate negative emotions
        if primary_emotion in [EmotionType.SADNESS, EmotionType.ANXIETY, EmotionType.GUILT] and intensity > 0.4:
            return TherapeuticTechnique.COGNITIVE_REFRAMING
        
        # Mindfulness for general emotional regulation
        if intensity > 0.5:
            return TherapeuticTechnique.MINDFULNESS
        
        # Default to active listening
        return TherapeuticTechnique.ACTIVE_LISTENING
    
    async def generate_empathetic_response(
        self,
        emotional_state: EmotionalState,
        user_message: str,
        technique: TherapeuticTechnique = None
    ) -> str:
        """Generate an empathetic response using therapeutic techniques."""
        try:
            if not technique:
                technique = await self.suggest_therapeutic_technique(emotional_state)
            
            # Use Gemini for therapeutic response generation
            response = await self.gemini_service.generate_therapeutic_response(
                user_message,
                emotional_state.model_dump(),
                technique
            )
            
            if response:
                return response
            
            # Fallback to template-based responses
            return self._generate_template_response(emotional_state, technique)
            
        except Exception as e:
            self.logger.error(f"Error generating empathetic response: {e}")
            return "I hear you, and I'm here to listen. Your feelings matter to me."
    
    def _generate_template_response(
        self,
        emotional_state: EmotionalState,
        technique: TherapeuticTechnique
    ) -> str:
        """Generate response using templates as fallback."""
        templates = self.therapeutic_responses.get(technique, [])
        
        if templates:
            import random
            template = random.choice(templates)
            
            # Simple template variable replacement
            template = template.replace("{emotion}", emotional_state.primary_emotion.value)
            return template
        
        return "I'm here with you. Thank you for sharing what you're going through."
    
    def get_user_emotional_summary(self, user_id: str) -> Dict[str, Any]:
        """Get emotional summary for a user."""
        if user_id not in self.user_emotional_history:
            return {"status": "no_data"}
        
        history = self.user_emotional_history[user_id]
        patterns = self.user_patterns.get(user_id, {})
        
        return {
            "total_interactions": len(history),
            "recent_emotions": [state.primary_emotion.value for state in history[-10:]],
            "patterns": patterns,
            "last_interaction": history[-1].timestamp if history else None
        }
