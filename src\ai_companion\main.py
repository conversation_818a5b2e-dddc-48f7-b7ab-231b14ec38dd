"""
AI Companion System - Main Entry Point

A conversational AI system for emotional support and mental health.
Integrates advanced emotional intelligence, memory architecture, and crisis detection.
"""

import asyncio
import logging
import time
import sys
import signal
from datetime import datetime, timezone
from typing import Dict, Any, Optional
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from ai_companion.config.settings import settings, validate_settings
from ai_companion.utils.logging import setup_logging
from ai_companion.utils.monitoring import PerformanceMonitor
from ai_companion.interfaces.gradio_app import GradioInterface
from ai_companion.interfaces.api import create_api_app
from ai_companion.core.conversation import ConversationService
from ai_companion.core.memory import MemoryService
from ai_companion.core.emotions import EmotionalIntelligenceService
from ai_companion.services.gemini import GeminiService
from ai_companion.services.storage import StorageService
from ai_companion.mental_health.crisis_detection import CrisisDetectionService
from ai_companion.mental_health.analytics import MentalHealthAnalytics


class AICompanionSystem:
    """Main AI Companion System orchestrating all components."""
    
    def __init__(self):
        """Initialize the AI companion system."""
        self.startup_time = time.time()
        self.system_ready = False
        
        # Core services
        self.gemini_service: Optional[GeminiService] = None
        self.storage_service: Optional[StorageService] = None
        self.memory_service: Optional[MemoryService] = None
        self.emotional_intelligence: Optional[EmotionalIntelligenceService] = None
        self.conversation_service: Optional[ConversationService] = None
        self.crisis_detection: Optional[CrisisDetectionService] = None
        self.mental_health_analytics: Optional[MentalHealthAnalytics] = None
        
        # Interfaces
        self.gradio_interface: Optional[GradioInterface] = None
        self.api_app = None
        
        # Monitoring
        self.performance_monitor = PerformanceMonitor()
        
        # System metrics
        self.metrics = {
            'total_conversations': 0,
            'crisis_interventions': 0,
            'average_response_time': 0.0,
            'system_uptime': 0.0,
            'active_users': 0
        }
        
        # Setup logging
        setup_logging()
        self.logger = logging.getLogger(__name__)
        self.logger.info("AI Companion System initialized")
    
    async def initialize(self):
        """Initialize all system components."""
        try:
            self.logger.info("🚀 Initializing AI Companion System...")
            
            # Validate configuration
            validate_settings()
            self.logger.info("✅ Configuration validated")
            
            # Initialize core services
            await self._initialize_core_services()
            
            # Initialize mental health components
            await self._initialize_mental_health_services()
            
            # Initialize interfaces
            await self._initialize_interfaces()
            
            # Start monitoring
            asyncio.create_task(self._system_monitor())
            
            self.startup_time = time.time() - self.startup_time
            self.system_ready = True
            
            self.logger.info(f"🎉 AI Companion System ready in {self.startup_time:.2f} seconds!")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize system: {e}")
            raise
    
    async def _initialize_core_services(self):
        """Initialize core AI services."""
        self.logger.info("🧠 Initializing core AI services...")
        
        # Initialize services in dependency order
        self.gemini_service = GeminiService()
        self.storage_service = StorageService()
        self.memory_service = MemoryService(self.storage_service)
        self.emotional_intelligence = EmotionalIntelligenceService(self.gemini_service)
        self.conversation_service = ConversationService(
            memory_service=self.memory_service,
            emotional_intelligence=self.emotional_intelligence,
            gemini_service=self.gemini_service
        )
        
        # Test core services
        await self._test_core_services()
        
        self.logger.info("✅ Core services initialized")
    
    async def _initialize_mental_health_services(self):
        """Initialize mental health specific services."""
        self.logger.info("🏥 Initializing mental health services...")
        
        self.crisis_detection = CrisisDetectionService(self.emotional_intelligence)
        self.mental_health_analytics = MentalHealthAnalytics(self.storage_service)
        
        self.logger.info("✅ Mental health services initialized")
    
    async def _initialize_interfaces(self):
        """Initialize user interfaces."""
        self.logger.info("🌐 Initializing interfaces...")
        
        # Create Gradio interface
        self.gradio_interface = GradioInterface(
            conversation_service=self.conversation_service,
            crisis_detection=self.crisis_detection
        )
        
        # Create API app
        self.api_app = create_api_app(
            conversation_service=self.conversation_service,
            mental_health_analytics=self.mental_health_analytics
        )
        
        self.logger.info("✅ Interfaces initialized")
    
    async def _test_core_services(self):
        """Test core services to ensure they're working."""
        try:
            # Test Gemini service (allow quota errors)
            try:
                test_response = await self.gemini_service.generate_response("Hello, this is a test.")
                if not test_response:
                    self.logger.warning("⚠️ Gemini service test failed - may be quota limited")
            except Exception as e:
                if "quota" in str(e).lower() or "429" in str(e):
                    self.logger.warning("⚠️ Gemini API quota exceeded - service configured but rate limited")
                else:
                    raise Exception(f"Gemini service test failed: {e}")
            
            # Test memory service
            test_memory = await self.memory_service.store_memory(
                user_id="test_user",
                content="Test memory",
                interaction_type="conversation"
            )
            if not test_memory:
                raise Exception("Memory service test failed")
            
            # Test emotional intelligence
            test_emotion = await self.emotional_intelligence.analyze_emotion("I'm feeling happy today!")
            if not test_emotion:
                raise Exception("Emotional intelligence test failed")
            
            self.logger.info("✅ All core services tested successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Service testing failed: {e}")
            raise
    
    async def _system_monitor(self):
        """Background system monitoring."""
        while True:
            try:
                # Update system metrics
                self.metrics['system_uptime'] = time.time() - (time.time() - self.startup_time)
                
                # Record performance metrics
                self.performance_monitor.record_metric('system_uptime', self.metrics['system_uptime'])
                
                # Log system status periodically
                if int(time.time()) % 300 == 0:  # Every 5 minutes
                    self.logger.info(f"System status: {self.metrics['total_conversations']} conversations, "
                                  f"{self.metrics['average_response_time']:.3f}s avg response time")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error in system monitor: {e}")
                await asyncio.sleep(30)
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get comprehensive system metrics."""
        return {
            **self.metrics,
            "system_ready": self.system_ready,
            "startup_time": self.startup_time,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "performance": self.performance_monitor.get_metrics()
        }
    
    async def run_gradio(self, share: bool = False, port: int = None):
        """Run Gradio interface."""
        if not self.gradio_interface:
            raise RuntimeError("Gradio interface not initialized")
        
        port = port or settings.gradio_port
        self.logger.info(f"🌐 Starting Gradio interface on port {port}")
        
        await self.gradio_interface.launch(
            server_name="0.0.0.0",
            server_port=port,
            share=share
        )
    
    async def run_api(self, host: str = "0.0.0.0", port: int = None):
        """Run FastAPI server."""
        if not self.api_app:
            raise RuntimeError("API app not initialized")
        
        port = port or settings.api_port
        self.logger.info(f"🚀 Starting API server on {host}:{port}")
        
        import uvicorn
        config = uvicorn.Config(
            app=self.api_app,
            host=host,
            port=port,
            log_level="info"
        )
        
        server = uvicorn.Server(config)
        await server.serve()
    
    async def shutdown(self):
        """Graceful shutdown of all components."""
        self.logger.info("🛑 Shutting down AI Companion System...")
        
        try:
            # Shutdown services
            if self.storage_service:
                await self.storage_service.close()
            
            self.logger.info("✅ System shutdown complete")
            
        except Exception as e:
            self.logger.error(f"Error during shutdown: {e}")


# Global system instance
ai_companion_system = AICompanionSystem()


# Signal handlers for graceful shutdown
def signal_handler(signum, frame):
    """Handle shutdown signals."""
    logger = logging.getLogger(__name__)
    logger.info(f"Received signal {signum}, initiating graceful shutdown...")
    asyncio.create_task(ai_companion_system.shutdown())
    sys.exit(0)


signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


async def main():
    """Main application entry point."""
    try:
        # Initialize system
        await ai_companion_system.initialize()
        
        # Run Gradio interface by default
        await ai_companion_system.run_gradio(
            share=False,
            port=settings.gradio_port
        )
        
    except KeyboardInterrupt:
        ai_companion_system.logger.info("Received keyboard interrupt")
    except Exception as e:
        ai_companion_system.logger.error(f"Error in main: {e}")
        raise
    finally:
        await ai_companion_system.shutdown()


if __name__ == "__main__":
    asyncio.run(main())
