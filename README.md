# 🧠 AI Companion System

A production-ready conversational AI for emotional support and mental health, featuring advanced emotional intelligence, dual-memory architecture, crisis detection, and therapeutic conversation capabilities.

---

## What the Project Does

- **Conversational AI** for emotional support and mental health.
- **Real-time emotion detection** and empathetic response generation.
- **Crisis detection** and intervention with escalation protocols.
- **Dual-memory system**: personal and universal knowledge.
- **Therapeutic techniques**: validation, reframing, mindfulness, and more.
- **Multiple interfaces**: Web UI (Gradio), REST API (FastAPI), CLI, WhatsApp integration.
- **Anonymized analytics** for research and professional use.

---

## How It Works (Technical Overview)

- **Startup**: `main.py` initializes all services, validates config, and launches interfaces.
- **Conversation Flow**:
  1. User input received (via UI/API/CLI).
  2. Emotion detected (pattern + Gemini AI).
  3. Relevant memories retrieved (personal/universal).
  4. Gemini API generates a contextual, empathetic response.
  5. Crisis detection assesses risk and triggers intervention if needed.
  6. Response delivered, memory updated, analytics recorded.
- **Memory**: Dual architecture (personal, universal), with privacy and emotional weighting.
- **Emotion**: Pattern-based and AI-powered detection, risk assessment, and therapeutic response.
- **Crisis**: Pattern-based detection, escalation, and resource recommendation.
- **Analytics**: Anonymized insights for users and population-level trends.

---

## Setup & Running (Step-by-Step)

### 1. Prerequisites

- Python 3.9+ (3.11 recommended)
- Google Gemini API key ([get one here](https://makersuite.google.com/app/apikey))
- Git
- Redis (optional, for caching)
- 4GB+ RAM recommended

### 2. Installation

#### Automated Setup

```bash
git clone <your-repo-url>
cd ai-companion-system
python scripts/dev_setup.py
```

#### Manual Setup

```bash
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
pip install --upgrade pip
pip install -r requirements.txt
pip install -r requirements-dev.txt  # For development
mkdir -p data/{db,logs,cache}
cp .env.example .env
```

#### Docker

```bash
docker-compose up -d
# or
docker build -t ai-companion .
docker run -p 7860:7860 -p 8000:8000 --env-file .env ai-companion
```

### 3. Configuration

- Copy `.env.example` to `.env` and fill in your Gemini API key and other settings.

### 4. Running

- **Web UI (Gradio):**
  ```bash
  make run
  # or
  python -m src.ai_companion.main
  ```
- **API Server:**
  ```bash
  make run-api
  # or
  uvicorn src.ai_companion.interfaces.api:app --host 0.0.0.0 --port 8000
  ```

### 5. Testing

```bash
make test
# or
pytest tests/
```

---

## Examples & Usage Tips

- **Send a message via API:**
  ```http
  POST /api/v1/conversation/message
  {
    "user_id": "user123",
    "message": "I'm feeling anxious about work",
    "interaction_type": "conversation"
  }
  ```
- **Web UI**: Visit http://localhost:7860
- **API Docs**: http://localhost:8000/docs

---

## Bad Practices, Errors, and Improvements

### Issues Identified

- Large files (>400 lines) in core/services: split for maintainability.
- Some redundancy in data models (e.g., duplicate fields).
- Test files outside `tests/` directory.
- Data/log/cache files committed: should be gitignored.
- `.codex-checkpoints/`, `.pytest_cache/`, `__pycache__/` in repo: should be ignored.
- Potential for circular imports.
- Logging setup: ensure no duplicate handlers.
- Documentation spread across multiple files: consolidate.

### Recommendations

- Refactor large files into smaller, focused modules.
- Move all test files into `tests/`.
- Add/verify `.gitignore` for all cache, log, and db files.
- Remove unnecessary folders from version control.
- Consolidate documentation into a single `README.md`.
- Add/verify type annotations and docstrings.
- Review and optimize imports to avoid circular dependencies.
- Consider splitting API schemas/models from business logic.

---

## Contributing

- Fork, branch, and submit PRs.
- Write tests for new features.
- Format and lint code before submitting.
- See full contributing guidelines in the repo.

---

## License

MIT License. See [LICENSE](LICENSE) for details.

---

*Built for human emotional wellbeing and mental health support.*