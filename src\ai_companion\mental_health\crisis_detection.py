"""
Crisis Detection Service for the AI Companion System.
Advanced crisis intervention and emergency response capabilities.
"""

import asyncio
import logging
import re
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict

from ..core.models import (
    EmotionType, MentalHealthRisk, TherapeuticTechnique,
    EmotionalState, InteractionType, utc_now
)
from ..core.emotions import EmotionalIntelligenceService
from ..config.settings import settings
from ..utils.helpers import generate_id


class CrisisDetectionService:
    """Advanced crisis detection and intervention service."""
    
    def __init__(self, emotional_intelligence: EmotionalIntelligenceService):
        """Initialize crisis detection service."""
        self.logger = logging.getLogger(__name__)
        self.emotional_intelligence = emotional_intelligence
        
        # Crisis detection patterns
        self.crisis_patterns = self._initialize_crisis_patterns()
        self.intervention_strategies = self._initialize_intervention_strategies()
        self.crisis_resources = self._initialize_crisis_resources()
        
        # Crisis tracking
        self.active_crises: Dict[str, Dict[str, Any]] = {}
        self.crisis_history: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        
        # Escalation thresholds
        self.escalation_thresholds = {
            "immediate_danger": 0.9,
            "high_risk": 0.7,
            "moderate_risk": 0.4,
            "watch_list": 0.2
        }
        
        self.logger.info("✅ Crisis Detection Service initialized")
    
    def _initialize_crisis_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Initialize crisis detection patterns with severity weights."""
        return {
            "suicide_ideation": {
                "patterns": [
                    r'\b(kill myself|end it all|suicide|take my life|don\'t want to live)\b',
                    r'\b(better off dead|no point living|want to die|end the pain)\b',
                    r'\b(suicide plan|how to die|ways to kill|ending it)\b',
                    r'\b(goodbye|final message|last time|won\'t see you again)\b'
                ],
                "severity": 0.9,
                "intervention_type": "immediate"
            },
            "self_harm": {
                "patterns": [
                    r'\b(cut myself|hurt myself|self harm|cutting|burning myself)\b',
                    r'\b(deserve pain|punish myself|make it stop|physical pain)\b',
                    r'\b(razor|blade|pills|overdose|harm)\b',
                    r'\b(cutting tools|self injury|self mutilation)\b'
                ],
                "severity": 0.8,
                "intervention_type": "urgent"
            },
            "severe_depression": {
                "patterns": [
                    r'\b(can\'t go on|no hope|hopeless|pointless|empty inside)\b',
                    r'\b(nothing matters|can\'t feel|numb|void|darkness)\b',
                    r'\b(burden|worthless|everyone better without me)\b',
                    r'\b(can\'t take it|too much|overwhelming|drowning)\b'
                ],
                "severity": 0.6,
                "intervention_type": "priority"
            },
            "panic_crisis": {
                "patterns": [
                    r'\b(can\'t breathe|heart racing|dying|losing control|going crazy)\b',
                    r'\b(panic attack|hyperventilating|chest pain|dizzy|faint)\b',
                    r'\b(emergency|help me|can\'t stop|overwhelming fear)\b',
                    r'\b(losing my mind|going insane|reality slipping)\b'
                ],
                "severity": 0.7,
                "intervention_type": "immediate"
            },
            "substance_abuse_crisis": {
                "patterns": [
                    r'\b(overdose|too many pills|drinking too much|can\'t stop using)\b',
                    r'\b(addiction|substance|drugs|alcohol|relapse)\b',
                    r'\b(withdrawal|detox|need help|can\'t quit)\b'
                ],
                "severity": 0.7,
                "intervention_type": "urgent"
            },
            "domestic_violence": {
                "patterns": [
                    r'\b(hitting me|abusing me|violent|threatening|scared for safety)\b',
                    r'\b(domestic violence|partner violence|afraid to go home)\b',
                    r'\b(bruises|injuries|hiding|escape|safe place)\b'
                ],
                "severity": 0.8,
                "intervention_type": "immediate"
            }
        }
    
    def _initialize_intervention_strategies(self) -> Dict[str, Dict[str, Any]]:
        """Initialize intervention strategies by crisis type."""
        return {
            "suicide_ideation": {
                "immediate_response": "I'm very concerned about what you've shared. Your life has value and meaning. You don't have to go through this alone.",
                "safety_planning": True,
                "professional_referral": True,
                "follow_up_required": True,
                "techniques": [TherapeuticTechnique.CRISIS_INTERVENTION, TherapeuticTechnique.VALIDATION]
            },
            "self_harm": {
                "immediate_response": "I hear that you're in pain and looking for ways to cope. Let's find safer ways to manage these intense feelings.",
                "safety_planning": True,
                "professional_referral": True,
                "follow_up_required": True,
                "techniques": [TherapeuticTechnique.GROUNDING, TherapeuticTechnique.VALIDATION]
            },
            "severe_depression": {
                "immediate_response": "I can hear how much you're struggling right now. These feelings are overwhelming, but they can change with support.",
                "safety_planning": False,
                "professional_referral": True,
                "follow_up_required": True,
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.ENCOURAGEMENT]
            },
            "panic_crisis": {
                "immediate_response": "You're having a panic attack, but you're safe. Let's focus on your breathing and grounding techniques.",
                "safety_planning": False,
                "professional_referral": False,
                "follow_up_required": False,
                "techniques": [TherapeuticTechnique.GROUNDING, TherapeuticTechnique.MINDFULNESS]
            },
            "substance_abuse_crisis": {
                "immediate_response": "Addiction is a medical condition, not a moral failing. There are people and resources that can help you.",
                "safety_planning": True,
                "professional_referral": True,
                "follow_up_required": True,
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.PROBLEM_SOLVING]
            },
            "domestic_violence": {
                "immediate_response": "Your safety is the most important thing. You deserve to be safe and treated with respect.",
                "safety_planning": True,
                "professional_referral": True,
                "follow_up_required": True,
                "techniques": [TherapeuticTechnique.VALIDATION, TherapeuticTechnique.CRISIS_INTERVENTION]
            }
        }
    
    def _initialize_crisis_resources(self) -> Dict[str, List[Dict[str, str]]]:
        """Initialize crisis support resources."""
        return {
            "suicide_prevention": [
                {
                    "name": "National Suicide Prevention Lifeline",
                    "phone": "988",
                    "description": "24/7 crisis support",
                    "website": "https://suicidepreventionlifeline.org/"
                },
                {
                    "name": "Crisis Text Line",
                    "phone": "Text HOME to 741741",
                    "description": "24/7 text-based crisis support",
                    "website": "https://www.crisistextline.org/"
                }
            ],
            "mental_health": [
                {
                    "name": "SAMHSA National Helpline",
                    "phone": "1-************",
                    "description": "Mental health and substance abuse treatment referrals",
                    "website": "https://www.samhsa.gov/find-help/national-helpline"
                },
                {
                    "name": "Mental Health America",
                    "phone": "N/A",
                    "description": "Mental health resources and screening tools",
                    "website": "https://www.mhanational.org/"
                }
            ],
            "domestic_violence": [
                {
                    "name": "National Domestic Violence Hotline",
                    "phone": "1-************",
                    "description": "24/7 domestic violence support",
                    "website": "https://www.thehotline.org/"
                }
            ],
            "substance_abuse": [
                {
                    "name": "SAMHSA National Helpline",
                    "phone": "1-************",
                    "description": "Substance abuse treatment referrals",
                    "website": "https://www.samhsa.gov/find-help/national-helpline"
                }
            ],
            "emergency": [
                {
                    "name": "Emergency Services",
                    "phone": "911",
                    "description": "Immediate emergency response",
                    "website": "N/A"
                }
            ]
        }
    
    async def analyze_message(self, message: str, user_id: str) -> Dict[str, Any]:
        """Analyze message for crisis indicators."""
        try:
            # Get emotional analysis first
            emotional_state = await self.emotional_intelligence.analyze_emotion(
                message, user_id, {"crisis_analysis": True}
            )
            
            # Detect crisis patterns
            crisis_indicators = self._detect_crisis_patterns(message)
            
            # Calculate overall crisis score
            crisis_score = self._calculate_crisis_score(crisis_indicators, emotional_state)
            
            # Determine crisis level and type
            is_crisis, crisis_level, crisis_type = self._assess_crisis_level(
                crisis_score, crisis_indicators
            )
            
            # Generate intervention response
            intervention_response = await self._generate_intervention_response(
                crisis_type, emotional_state, message
            )
            
            # Track crisis if detected
            if is_crisis:
                await self._track_crisis_event(user_id, {
                    "message": message,
                    "crisis_type": crisis_type,
                    "crisis_score": crisis_score,
                    "emotional_state": emotional_state.model_dump(),
                    "timestamp": utc_now()
                })
            
            return {
                "is_crisis": is_crisis,
                "crisis_type": crisis_type,
                "crisis_score": crisis_score,
                "risk_level": crisis_level,
                "intervention_message": intervention_response,
                "resources": self._get_relevant_resources(crisis_type),
                "emotional_state": emotional_state.model_dump(),
                "requires_follow_up": self._requires_follow_up(crisis_type),
                "safety_planning_needed": self._needs_safety_planning(crisis_type)
            }
            
        except Exception as e:
            self.logger.error(f"Error analyzing message for crisis: {e}")
            return {
                "is_crisis": False,
                "crisis_type": None,
                "crisis_score": 0.0,
                "risk_level": MentalHealthRisk.LOW,
                "intervention_message": "I'm here to listen and support you.",
                "resources": [],
                "emotional_state": {},
                "requires_follow_up": False,
                "safety_planning_needed": False
            }
    
    def _detect_crisis_patterns(self, message: str) -> Dict[str, float]:
        """Detect crisis patterns in message."""
        message_lower = message.lower()
        detected_patterns = {}
        
        for crisis_type, config in self.crisis_patterns.items():
            score = 0.0
            patterns = config["patterns"]
            severity = config["severity"]
            
            for pattern in patterns:
                matches = len(re.findall(pattern, message_lower, re.IGNORECASE))
                if matches > 0:
                    score += matches * severity * 0.3  # Weight per match
            
            if score > 0:
                detected_patterns[crisis_type] = min(score, 1.0)  # Cap at 1.0
        
        return detected_patterns
    
    def _calculate_crisis_score(
        self,
        crisis_indicators: Dict[str, float],
        emotional_state: EmotionalState
    ) -> float:
        """Calculate overall crisis score."""
        # Base score from pattern detection
        pattern_score = max(crisis_indicators.values()) if crisis_indicators else 0.0
        
        # Factor in emotional intensity and risk
        emotion_factor = 0.0
        if emotional_state.primary_emotion in [
            EmotionType.SADNESS, EmotionType.FEAR, EmotionType.ANXIETY,
            EmotionType.GUILT, EmotionType.SHAME
        ]:
            emotion_factor = emotional_state.intensity * 0.3
        
        # Risk level factor
        risk_factor = {
            MentalHealthRisk.LOW: 0.0,
            MentalHealthRisk.MODERATE: 0.2,
            MentalHealthRisk.HIGH: 0.5,
            MentalHealthRisk.CRITICAL: 0.8
        }.get(emotional_state.risk_level, 0.0)
        
        # Combine scores
        total_score = min(pattern_score + emotion_factor + risk_factor, 1.0)
        
        return total_score
    
    def _assess_crisis_level(
        self,
        crisis_score: float,
        crisis_indicators: Dict[str, float]
    ) -> Tuple[bool, MentalHealthRisk, Optional[str]]:
        """Assess crisis level and determine primary crisis type."""
        # Determine if this is a crisis
        is_crisis = crisis_score >= self.escalation_thresholds["watch_list"]
        
        # Determine crisis level
        if crisis_score >= self.escalation_thresholds["immediate_danger"]:
            crisis_level = MentalHealthRisk.CRITICAL
        elif crisis_score >= self.escalation_thresholds["high_risk"]:
            crisis_level = MentalHealthRisk.HIGH
        elif crisis_score >= self.escalation_thresholds["moderate_risk"]:
            crisis_level = MentalHealthRisk.MODERATE
        else:
            crisis_level = MentalHealthRisk.LOW
        
        # Determine primary crisis type
        crisis_type = None
        if crisis_indicators:
            # Get the crisis type with highest score
            crisis_type = max(crisis_indicators, key=crisis_indicators.get)
        
        return is_crisis, crisis_level, crisis_type
    
    async def _generate_intervention_response(
        self,
        crisis_type: Optional[str],
        emotional_state: EmotionalState,
        original_message: str
    ) -> str:
        """Generate appropriate intervention response."""
        if not crisis_type:
            return "I'm here to listen and support you. Your feelings are important to me."
        
        strategy = self.intervention_strategies.get(crisis_type, {})
        base_response = strategy.get("immediate_response", "I'm here to support you.")
        
        # Use emotional intelligence for personalized response
        techniques = strategy.get("techniques", [TherapeuticTechnique.VALIDATION])
        primary_technique = techniques[0] if techniques else TherapeuticTechnique.VALIDATION
        
        try:
            # Generate empathetic response using AI
            ai_response = await self.emotional_intelligence.generate_empathetic_response(
                emotional_state, original_message, primary_technique
            )
            
            if ai_response:
                return f"{base_response}\n\n{ai_response}"
            
        except Exception as e:
            self.logger.error(f"Error generating AI intervention response: {e}")
        
        return base_response
    
    def _get_relevant_resources(self, crisis_type: Optional[str]) -> List[Dict[str, str]]:
        """Get relevant crisis resources."""
        if not crisis_type:
            return []
        
        # Map crisis types to resource categories
        resource_mapping = {
            "suicide_ideation": ["suicide_prevention", "mental_health", "emergency"],
            "self_harm": ["suicide_prevention", "mental_health"],
            "severe_depression": ["mental_health", "suicide_prevention"],
            "panic_crisis": ["mental_health"],
            "substance_abuse_crisis": ["substance_abuse", "mental_health"],
            "domestic_violence": ["domestic_violence", "emergency"]
        }
        
        relevant_categories = resource_mapping.get(crisis_type, ["mental_health"])
        resources = []
        
        for category in relevant_categories:
            resources.extend(self.crisis_resources.get(category, []))
        
        return resources
    
    def _requires_follow_up(self, crisis_type: Optional[str]) -> bool:
        """Check if crisis type requires follow-up."""
        if not crisis_type:
            return False
        
        strategy = self.intervention_strategies.get(crisis_type, {})
        return strategy.get("follow_up_required", False)
    
    def _needs_safety_planning(self, crisis_type: Optional[str]) -> bool:
        """Check if crisis type needs safety planning."""
        if not crisis_type:
            return False
        
        strategy = self.intervention_strategies.get(crisis_type, {})
        return strategy.get("safety_planning", False)
    
    async def _track_crisis_event(self, user_id: str, crisis_data: Dict[str, Any]):
        """Track crisis event for monitoring and follow-up."""
        try:
            # Add to active crises if high severity
            if crisis_data.get("crisis_score", 0) >= self.escalation_thresholds["high_risk"]:
                self.active_crises[user_id] = {
                    **crisis_data,
                    "status": "active",
                    "interventions": [],
                    "last_contact": utc_now()
                }
            
            # Add to crisis history
            self.crisis_history[user_id].append(crisis_data)
            
            # Keep history manageable
            if len(self.crisis_history[user_id]) > 50:
                self.crisis_history[user_id] = self.crisis_history[user_id][-30:]
            
            self.logger.warning(f"Crisis event tracked for user {user_id}: {crisis_data['crisis_type']}")
            
        except Exception as e:
            self.logger.error(f"Error tracking crisis event: {e}")
    
    async def handle_crisis_intervention(self, user_id: str, response_data: Dict[str, Any]):
        """Handle crisis intervention workflow."""
        try:
            crisis_type = response_data.get("crisis_type")
            if not crisis_type:
                return
            
            # Log intervention
            intervention = {
                "timestamp": utc_now(),
                "type": "ai_intervention",
                "crisis_type": crisis_type,
                "response": response_data.get("response", ""),
                "resources_provided": response_data.get("resources", [])
            }
            
            # Update active crisis
            if user_id in self.active_crises:
                self.active_crises[user_id]["interventions"].append(intervention)
                self.active_crises[user_id]["last_contact"] = utc_now()
            
            # Schedule follow-up if needed
            if self._requires_follow_up(crisis_type):
                asyncio.create_task(self._schedule_follow_up(user_id, crisis_type))
            
        except Exception as e:
            self.logger.error(f"Error handling crisis intervention: {e}")
    
    async def _schedule_follow_up(self, user_id: str, crisis_type: str):
        """Schedule follow-up for crisis situations."""
        try:
            # Wait for follow-up interval (e.g., 1 hour for high-risk situations)
            follow_up_delay = 3600  # 1 hour in seconds
            
            if crisis_type in ["suicide_ideation", "self_harm"]:
                follow_up_delay = 1800  # 30 minutes for highest risk
            
            await asyncio.sleep(follow_up_delay)
            
            # Check if user is still in active crisis
            if user_id in self.active_crises:
                self.logger.info(f"Follow-up needed for user {user_id} - crisis type: {crisis_type}")
                # In a real implementation, this could trigger notifications
                # to human moderators or send check-in messages
            
        except Exception as e:
            self.logger.error(f"Error scheduling follow-up: {e}")
    
    def get_crisis_summary(self, user_id: str) -> Dict[str, Any]:
        """Get crisis summary for a user."""
        try:
            history = self.crisis_history.get(user_id, [])
            active_crisis = self.active_crises.get(user_id)
            
            if not history and not active_crisis:
                return {"status": "no_crisis_history"}
            
            # Analyze crisis patterns
            crisis_types = [event.get("crisis_type") for event in history if event.get("crisis_type")]
            crisis_frequency = {}
            for crisis_type in crisis_types:
                crisis_frequency[crisis_type] = crisis_frequency.get(crisis_type, 0) + 1
            
            return {
                "total_crisis_events": len(history),
                "active_crisis": bool(active_crisis),
                "crisis_frequency": crisis_frequency,
                "last_crisis": history[-1]["timestamp"] if history else None,
                "highest_risk_level": max([event.get("crisis_score", 0) for event in history], default=0)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting crisis summary: {e}")
            return {"status": "error", "message": str(e)}
